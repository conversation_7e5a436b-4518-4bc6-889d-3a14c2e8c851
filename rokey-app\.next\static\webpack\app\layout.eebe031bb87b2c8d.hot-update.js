"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a8d61697c4ff\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE4ZDYxNjk3YzRmZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/LayoutContent.tsx":
/*!******************************************!*\
  !*** ./src/components/LayoutContent.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LayoutContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _components_OptimisticLoadingScreen__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/OptimisticLoadingScreen */ \"(app-pages-browser)/./src/components/OptimisticLoadingScreen.tsx\");\n/* harmony import */ var _components_OptimisticPageLoader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/OptimisticPageLoader */ \"(app-pages-browser)/./src/components/OptimisticPageLoader.tsx\");\n/* harmony import */ var _hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAdvancedPreloading */ \"(app-pages-browser)/./src/hooks/useAdvancedPreloading.ts\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction LayoutContentInner(param) {\n    let { children } = param;\n    _s();\n    const { isCollapsed, isHovered, collapseSidebar } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__.useSidebar)();\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__.useNavigationSafe)();\n    const { isNavigating, targetRoute, isPageCached } = navigationContext || {\n        isNavigating: false,\n        targetRoute: null,\n        isPageCached: ()=>false\n    };\n    // Toast notifications\n    const { toasts, removeToast } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    // Track if we're on desktop (lg breakpoint and above)\n    const [isDesktop, setIsDesktop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LayoutContentInner.useEffect\": ()=>{\n            const checkIsDesktop = {\n                \"LayoutContentInner.useEffect.checkIsDesktop\": ()=>{\n                    setIsDesktop(window.innerWidth >= 1024); // lg breakpoint\n                }\n            }[\"LayoutContentInner.useEffect.checkIsDesktop\"];\n            // Check on mount\n            checkIsDesktop();\n            // Listen for resize events\n            window.addEventListener('resize', checkIsDesktop);\n            return ({\n                \"LayoutContentInner.useEffect\": ()=>window.removeEventListener('resize', checkIsDesktop)\n            })[\"LayoutContentInner.useEffect\"];\n        }\n    }[\"LayoutContentInner.useEffect\"], []);\n    // Calculate actual sidebar width based on collapsed and hover states\n    // Only apply sidebar width on desktop, mobile uses overlay\n    const sidebarWidth = isDesktop ? !isCollapsed || isHovered ? 256 : 64 : 0;\n    // Initialize advanced preloading system\n    (0,_hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_8__.useAdvancedPreloading)({\n        maxConcurrent: 2,\n        idleTimeout: 1500,\n        backgroundDelay: 3000\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block fixed left-0 top-0 h-full z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 z-50 \".concat(isCollapsed ? 'pointer-events-none' : ''),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: collapseSidebar,\n                        className: \"absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer \".concat(isCollapsed ? 'opacity-0' : 'opacity-50')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out \".concat(isCollapsed ? '-translate-x-full' : 'translate-x-0'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden min-w-0 transition-all duration-200 ease-out\",\n                style: {\n                    marginLeft: \"\".concat(sidebarWidth, \"px\")\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed top-0 right-0 z-30 transition-all duration-200 ease-out\",\n                        style: {\n                            left: \"\".concat(sidebarWidth, \"px\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto content-area mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 sm:p-6 lg:p-8 w-full \".concat(// When sidebar is expanded, use standard max width with centering\n                            // When sidebar is collapsed, use larger max width or no max width\n                            isDesktop && (!isCollapsed || isHovered) ? 'max-w-7xl mx-auto' : isDesktop ? 'max-w-none px-8' : 'max-w-7xl mx-auto'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-transition\",\n                                children: isNavigating && targetRoute ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptimisticPageLoader__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    targetRoute: targetRoute,\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 17\n                                }, this) : children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_9__.ToastContainer, {\n                toasts: toasts,\n                onRemove: removeToast\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_s(LayoutContentInner, \"rilvE/GaX21XzNPf5z7jhTgb3B0=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__.useSidebar,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__.useNavigationSafe,\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_9__.useToast,\n        _hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_8__.useAdvancedPreloading\n    ];\n});\n_c = LayoutContentInner;\nfunction LayoutContent(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptimisticLoadingScreen__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            targetRoute: null\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n            lineNumber: 125,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LayoutContentInner, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_c1 = LayoutContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"LayoutContentInner\");\n$RefreshReg$(_c1, \"LayoutContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LayoutContent.tsx\n"));

/***/ })

});